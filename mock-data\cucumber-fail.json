[{"description": "", "elements": [{"description": "", "id": "main-page-functionality;user-can-access-main-page", "keyword": "<PERSON><PERSON><PERSON>", "line": 4, "name": "User can access main page", "steps": [{"arguments": [], "keyword": "Given ", "line": 5, "name": "I am on the tracker app homepage", "result": {"duration": 2500000000, "status": "passed"}}, {"arguments": [], "keyword": "When ", "line": 6, "name": "I wait for the page to load", "result": {"duration": 1000000000, "status": "failed", "error_message": "Timeout waiting for page to load"}}, {"arguments": [], "keyword": "Then ", "line": 7, "name": "I should see the main navigation", "result": {"duration": 0, "status": "skipped"}}], "tags": [], "type": "scenario", "uri": "cypress/e2e/features/main-page.feature"}, {"description": "", "id": "main-page-functionality;user-can-navigate-to-search", "keyword": "<PERSON><PERSON><PERSON>", "line": 9, "name": "User can navigate to search", "steps": [{"arguments": [], "keyword": "Given ", "line": 10, "name": "I am on the tracker app homepage", "result": {"duration": 1500000000, "status": "passed"}}, {"arguments": [], "keyword": "When ", "line": 11, "name": "I click on the search button", "result": {"duration": 800000000, "status": "failed", "error_message": "Element not found: search button"}}, {"arguments": [], "keyword": "Then ", "line": 12, "name": "I should see the search interface", "result": {"duration": 0, "status": "skipped"}}], "tags": [], "type": "scenario", "uri": "cypress/e2e/features/main-page.feature"}], "id": "main-page-functionality", "keyword": "Feature", "line": 1, "name": "Main Page Functionality", "tags": [], "uri": "cypress/e2e/features/main-page.feature"}, {"description": "", "elements": [{"description": "", "id": "file-upload-functionality;user-can-upload-file", "keyword": "<PERSON><PERSON><PERSON>", "line": 4, "name": "User can upload file", "steps": [{"arguments": [], "keyword": "Given ", "line": 5, "name": "I am on the file upload page", "result": {"duration": 2000000000, "status": "passed"}}, {"arguments": [], "keyword": "When ", "line": 6, "name": "I select a file to upload", "result": {"duration": 1200000000, "status": "failed", "error_message": "File upload dialog did not open"}}, {"arguments": [], "keyword": "Then ", "line": 7, "name": "I should see upload progress", "result": {"duration": 0, "status": "skipped"}}], "tags": [], "type": "scenario", "uri": "cypress/e2e/features/file-screen.feature"}], "id": "file-upload-functionality", "keyword": "Feature", "line": 1, "name": "File Upload Functionality", "tags": [], "uri": "cypress/e2e/features/file-screen.feature"}]