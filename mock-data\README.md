# E2E Test Report Mock Data

This directory contains mock Cucumber JSON report files for testing the E2E report parsing functionality.

## Files

- `cucumber-pass.json` - All scenarios pass
- `cucumber-fail.json` - All scenarios fail
- `cucumber-skip.json` - All scenarios are skipped
- `cucumber-mixed.json` - Mixed results (pass, fail, skip)

## Usage

### Windows Testing
Run the batch file from the project root:
```cmd
test-e2e-report.bat
```

### Manual Testing
1. Copy one of the mock files to `client/cypress/reports/cucumber.json`
2. Run the report parser:
   ```bash
   .github/scripts/parse-report.sh
   ```

## Mock Data Structure

Each file contains Cucumber JSON format with:
- Feature definitions
- Scenario definitions
- Step definitions with results
- Status indicators: "passed", "failed", "skipped"
- Duration information
- Error messages for failed steps

## Test Scenarios

### Pass Scenario
- 2 features with 3 scenarios total
- All steps pass
- Expected result: SUCCESS status

### Fail Scenario
- 2 features with 3 scenarios total
- Multiple failed steps with error messages
- Expected result: FAILURE status

### Skip Scenario
- 2 features with 3 scenarios total
- All steps skipped
- Expected result: SUCCESS status (no failures)

### Mixed Scenario
- 3 features with 4 scenarios total
- 1 passing scenario, 1 failing scenario, 1 skipped scenario
- Expected result: FAILURE status (due to failures)

## Features Covered

The mock data covers the existing feature files in the project:
- `main-page.feature`
- `search-bar.feature`
- `file-screen.feature`
- `event-screen.feature`
