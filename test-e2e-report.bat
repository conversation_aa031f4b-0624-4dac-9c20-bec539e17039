@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Tracker App E2E Report Test
echo ========================================
echo.

REM Check if jq is available
where jq >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: jq is not installed or not in PATH
    echo Please install jq from https://stedolan.github.io/jq/download/
    echo.
    pause
    exit /b 1
)

REM Create test directories
if not exist "client\cypress\reports" mkdir "client\cypress\reports"
if not exist ".github\scripts" mkdir ".github\scripts"

echo Creating mock test data...
echo.

REM Set test scenarios
set "scenarios[0]=pass"
set "scenarios[1]=fail"
set "scenarios[2]=skip"
set "scenarios[3]=mixed"

for %%s in (pass fail skip mixed) do (
    echo Testing scenario: %%s
    
    REM Copy the appropriate mock data
    copy "mock-data\cucumber-%%s.json" "client\cypress\reports\cucumber.json" >nul 2>&1
    
    if exist "client\cypress\reports\cucumber.json" (
        echo   - Mock data copied successfully
        
        REM Set environment variables for testing
        set "GITHUB_RUN_NUMBER=123"
        set "GITHUB_REF_NAME=feature/test-e2e"
        set "GITHUB_SHA=abc1234567890"
        set "GITHUB_SERVER_URL=https://github.com"
        set "GITHUB_REPOSITORY=veritone/tracker-app"
        set "GITHUB_RUN_ID=456789"
        set "GITHUB_OUTPUT=test-output-%%s.txt"
        
        REM Run the report parser
        echo   - Running report parser...
        bash .github\scripts\parse-report.sh
        
        if exist "test-output-%%s.txt" (
            echo   - Report generated: test-output-%%s.txt
            echo   - Preview:
            echo   ----------------------------------------
            type "test-output-%%s.txt" | findstr /C:"SLACK_MESSAGE" | head -5
            echo   ----------------------------------------
        ) else (
            echo   - ERROR: Report not generated
        )
    ) else (
        echo   - ERROR: Failed to copy mock data
    )
    
    echo.
)

echo ========================================
echo Test completed!
echo Check the generated test-output-*.txt files for results
echo ========================================
pause
