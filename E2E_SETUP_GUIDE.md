# E2E Test Report Setup Guide

This guide explains how to use the adapted E2E test reporting system for the Tracker App project.

## 📁 Files Created/Modified

### GitHub Workflow Files
- `.github/workflows/daily_e2e.yml` - **ADAPTED** for Tracker App
- `.github/scripts/parse-report.sh` - **ADAPTED** for Tracker App  
- `.github/scripts/e2e-report-handler.jq` - **KEPT ORIGINAL** (works as-is)

### Testing Files
- `test-e2e-report.bat` - Windows batch script for testing
- `test-e2e-report.ps1` - PowerShell script for testing (recommended)
- `mock-data/` - Directory with test data files

### Mock Data Files
- `mock-data/cucumber-pass.json` - All tests pass scenario
- `mock-data/cucumber-fail.json` - All tests fail scenario  
- `mock-data/cucumber-skip.json` - All tests skipped scenario
- `mock-data/cucumber-mixed.json` - Mixed results scenario
- `mock-data/README.md` - Documentation for mock data

## 🔧 Key Adaptations Made

### 1. GitHub Workflow Changes
- **Runner**: Changed from `veritone-self-hosted-32gb` to `ubuntu-latest`
- **Node.js**: Updated to use `actions/setup-node@v4` with Node 22.x
- **Yarn Version**: Updated to match project's `yarn@4.1.1`
- **Project Structure**: Added separate client/server dependency installation
- **App Startup**: Modified to start both server and client separately
- **Health Check**: Changed from `landingPage.feature` to `main-page.feature`
- **Working Directory**: Added `working-directory: client` for Cypress commands
- **Slack Channel**: Changed from `team-dev-glc` to `tracker-dev`
- **Bot Name**: Changed to "Tracker App E2E Bot"

### 2. Report Parser Changes
- **Report Path**: Changed from `cypress/reports/cucumber.json` to `client/cypress/reports/cucumber.json`
- **Report Title**: Changed from "Illuminate Cucumber Test Report" to "Tracker App Cucumber Test Report"

### 3. JQ Script
- **No Changes**: The original `e2e-report-handler.jq` works perfectly as-is

## 🚀 How to Test on Windows

### Option 1: PowerShell (Recommended)
```powershell
# Test all scenarios
.\test-e2e-report.ps1

# Test specific scenario
.\test-e2e-report.ps1 -Scenario "pass"
.\test-e2e-report.ps1 -Scenario "fail"
.\test-e2e-report.ps1 -Scenario "skip"
.\test-e2e-report.ps1 -Scenario "mixed"
```

### Option 2: Batch File
```cmd
test-e2e-report.bat
```

## 📋 Prerequisites

### For Testing
- **jq**: JSON processor tool
  - Install via Chocolatey: `choco install jq`
  - Install via Scoop: `scoop install jq`
  - Download from: https://stedolan.github.io/jq/download/
- **bash**: Available through Git for Windows or WSL

### For GitHub Actions
- **Secrets Required**:
  - `CYPRESS_USERNAME` - Test user credentials
  - `CYPRESS_PASSWORD` - Test user credentials  
  - `SLACK_WEBHOOK` - Slack webhook URL (optional)

## 🎯 Expected Test Results

### Pass Scenario
- Status: SUCCESS
- 2 features, 3 scenarios total
- All scenarios pass

### Fail Scenario  
- Status: FAILURE
- 2 features, 3 scenarios total
- Multiple failed scenarios with error details

### Skip Scenario
- Status: SUCCESS
- 2 features, 3 scenarios total  
- All scenarios skipped (no failures)

### Mixed Scenario
- Status: FAILURE
- 3 features, 4 scenarios total
- 1 pass, 1 fail, 1 skip

## 🔍 Output Files

After running tests, check these files:
- `test-output-pass.txt` - Pass scenario results
- `test-output-fail.txt` - Fail scenario results
- `test-output-skip.txt` - Skip scenario results
- `test-output-mixed.txt` - Mixed scenario results

## 🚦 Next Steps

1. **Test Locally**: Run the PowerShell script to verify everything works
2. **Update Secrets**: Add required GitHub secrets for your repository
3. **Customize Slack**: Update the Slack channel and webhook as needed
4. **Run Workflow**: Trigger the workflow manually or wait for the daily schedule
5. **Monitor Results**: Check the generated reports and Slack notifications

## 🛠 Troubleshooting

### Common Issues
- **jq not found**: Install jq using package manager
- **bash not found**: Install Git for Windows or enable WSL
- **Permission denied**: Run PowerShell as Administrator
- **Mock data not found**: Ensure you're running from project root directory

### Cypress Configuration
The workflow expects:
- Cypress tests in `client/cypress/e2e/features/`
- Reports generated in `client/cypress/reports/cucumber.json`
- Existing feature files: `main-page.feature`, `search-bar.feature`, etc.
