# PowerShell script to test E2E report functionality on Windows
param(
    [string]$Scenario = "all"
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Tracker App E2E Report Test (PowerShell)" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if jq is available
try {
    $jqVersion = & jq --version 2>$null
    Write-Host "✓ jq found: $jqVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ ERROR: jq is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install jq from https://stedolan.github.io/jq/download/" -ForegroundColor Yellow
    Write-Host "Or use chocolatey: choco install jq" -ForegroundColor Yellow
    Write-Host "Or use scoop: scoop install jq" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if bash is available (Git Bash, WSL, etc.)
try {
    $bashVersion = & bash --version 2>$null | Select-Object -First 1
    Write-Host "✓ bash found: $($bashVersion.Split(' ')[3])" -ForegroundColor Green
} catch {
    Write-Host "✗ ERROR: bash is not available" -ForegroundColor Red
    Write-Host "Please install Git for Windows or WSL" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Create test directories
$directories = @("client\cypress\reports", ".github\scripts")
foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✓ Created directory: $dir" -ForegroundColor Green
    }
}

Write-Host "Creating mock test data..." -ForegroundColor Yellow
Write-Host ""

# Define test scenarios
$scenarios = @("pass", "fail", "skip", "mixed")

if ($Scenario -ne "all" -and $scenarios -contains $Scenario) {
    $scenarios = @($Scenario)
}

foreach ($testScenario in $scenarios) {
    Write-Host "Testing scenario: $testScenario" -ForegroundColor Magenta
    
    # Copy the appropriate mock data
    $sourceFile = "mock-data\cucumber-$testScenario.json"
    $targetFile = "client\cypress\reports\cucumber.json"
    
    if (Test-Path $sourceFile) {
        Copy-Item $sourceFile $targetFile -Force
        Write-Host "  ✓ Mock data copied successfully" -ForegroundColor Green
        
        # Set environment variables for testing
        $env:GITHUB_RUN_NUMBER = "123"
        $env:GITHUB_REF_NAME = "feature/test-e2e"
        $env:GITHUB_SHA = "abc1234567890"
        $env:GITHUB_SERVER_URL = "https://github.com"
        $env:GITHUB_REPOSITORY = "veritone/tracker-app"
        $env:GITHUB_RUN_ID = "456789"
        $env:GITHUB_OUTPUT = "test-output-$testScenario.txt"
        
        # Run the report parser using bash
        Write-Host "  ⚙ Running report parser..." -ForegroundColor Blue
        try {
            & bash .github\scripts\parse-report.sh
            
            if (Test-Path "test-output-$testScenario.txt") {
                Write-Host "  ✓ Report generated: test-output-$testScenario.txt" -ForegroundColor Green
                Write-Host "  📄 Preview:" -ForegroundColor Cyan
                Write-Host "  ----------------------------------------" -ForegroundColor Gray
                
                # Show preview of the generated report
                $content = Get-Content "test-output-$testScenario.txt" -Raw
                $slackMessage = ($content -split "SLACK_MESSAGE<<EOF")[1] -split "EOF")[0]
                $preview = ($slackMessage -split "`n")[0..4] -join "`n"
                Write-Host $preview -ForegroundColor White
                Write-Host "  ----------------------------------------" -ForegroundColor Gray
                
                # Show status
                if ($content -match "SLACK_MESSAGE_STATUS=(.+)") {
                    $status = $matches[1]
                    $statusColor = if ($status -eq "SUCCESS") { "Green" } else { "Red" }
                    Write-Host "  📊 Status: $status" -ForegroundColor $statusColor
                }
            } else {
                Write-Host "  ✗ ERROR: Report not generated" -ForegroundColor Red
            }
        } catch {
            Write-Host "  ✗ ERROR: Failed to run report parser" -ForegroundColor Red
            Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "  ✗ ERROR: Mock data file not found: $sourceFile" -ForegroundColor Red
    }
    
    Write-Host ""
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Test completed!" -ForegroundColor Green
Write-Host "Check the generated test-output-*.txt files for results" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan

# Offer to open results
$openResults = Read-Host "Would you like to open the results folder? (y/n)"
if ($openResults -eq "y" -or $openResults -eq "Y") {
    Start-Process explorer.exe -ArgumentList (Get-Location).Path
}
